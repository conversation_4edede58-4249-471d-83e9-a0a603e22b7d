// ==UserScript==
// @name         Auto HDR Ultra Optimized
// @namespace    http://taeparlaytampermonkey.net/
// @version      2.2
// @description  Ultra-optimized HDR effect with minimal overhead, smart caching, and efficient processing.
// <AUTHOR>
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    const SCRIPT_NAME = 'AutoHDRSettings';

    // Ultra-efficient caching system
    const processedElements = new WeakSet();
    const crossOriginCache = new Map();
    let intersectionObserver = null;
    let mutationObserver = null;

    // Pre-compiled filter strings for performance
    let cssFilterString = '';
    let siteExclusionCache = null;
    let lastHref = '';

    // --- Default Settings (Optimized for Better Visual Experience) ---
    const DEFAULT_SETTINGS = {
        hdrEnabled: true,
        brightness: 1.05,        // Slightly brighter for better visibility
        contrast: 1.15,          // More contrast for HDR effect
        saturation: 1.20,        // Enhanced colors (affects all colors equally)
        vibrance: 0.30,          // NEW: Smart color enhancement (protects skin tones, enhances muted colors)
        highlightReduction: 0.35, // Reduced for more natural highlights
        highlightThreshold: 240,  // Higher threshold for better balance
        shadowBoost: 0.15,       // NEW: Boost shadows for better dynamic range
        shadowThreshold: 50,     // NEW: Shadow enhancement threshold
        colorTemperature: 0,     // NEW: Color temperature adjustment (-100 to 100)
        sharpness: 0,           // NEW: Image sharpening (0 to 100)
        antiNoise: 0.3,         // NEW: Noise reduction strength (0 to 1)
        smoothing: 0.2,         // NEW: Image smoothing for pixelation (0 to 1)
        upscaleSmoothing: true, // NEW: Enable upscale smoothing for low-res content
        videoEnhancement: true, // NEW: Enhanced video processing
        excludedSites: [],       // Empty by default
        maxCanvasDimension: 2000,
        processSVGs: false,
        enableGUISettings: true,
        lazyProcessing: true,
        processOnlyVisible: true,
        autoDetectHDR: true,     // NEW: Auto-detect HDR content
        presets: {               // NEW: Quick presets with anti-noise, smoothing, and vibrance
            natural: {
                brightness: 1.02, contrast: 1.08, saturation: 1.02, vibrance: 0.15,
                antiNoise: 0.1, smoothing: 0.1, upscaleSmoothing: true, videoEnhancement: true
            },
            vivid: {
                brightness: 1.08, contrast: 1.25, saturation: 1.15, vibrance: 0.45,
                antiNoise: 0.2, smoothing: 0.15, upscaleSmoothing: true, videoEnhancement: true
            },
            cinema: {
                brightness: 0.95, contrast: 1.20, saturation: 1.10, vibrance: 0.25,
                antiNoise: 0.4, smoothing: 0.3, upscaleSmoothing: true, videoEnhancement: true
            },
            gaming: {
                brightness: 1.10, contrast: 1.30, saturation: 1.20, vibrance: 0.40,
                antiNoise: 0.3, smoothing: 0.2, sharpness: 20, upscaleSmoothing: true, videoEnhancement: true
            }
        }
    };

    let settings = { ...DEFAULT_SETTINGS };

    // --- Settings Management (Optimized) ---
    function loadSettings() {
        const saved = GM_getValue(SCRIPT_NAME, null);
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                settings = { ...DEFAULT_SETTINGS, ...parsed };
            } catch (e) {
                console.error("AutoHDR: Error parsing saved settings, using defaults.", e);
                settings = { ...DEFAULT_SETTINGS };
            }
        } else {
            settings = { ...DEFAULT_SETTINGS };
        }

        // Optimized type validation with single pass
        validateAndNormalizeSettings();
    }

    function validateAndNormalizeSettings() {
        // Batch validation for better performance
        const numericFields = {
            brightness: parseFloat,
            contrast: parseFloat,
            saturation: parseFloat,
            vibrance: parseFloat,
            highlightReduction: parseFloat,
            highlightThreshold: (val) => parseInt(val, 10),
            shadowBoost: parseFloat,
            shadowThreshold: (val) => parseInt(val, 10),
            colorTemperature: (val) => parseInt(val, 10),
            sharpness: (val) => parseInt(val, 10),
            antiNoise: parseFloat,
            smoothing: parseFloat,
            maxCanvasDimension: (val) => parseInt(val, 10)
        };

        for (const [field, parser] of Object.entries(numericFields)) {
            const parsed = parser(settings[field]);
            settings[field] = isNaN(parsed) ? DEFAULT_SETTINGS[field] : parsed;
        }

        // Range validation for new settings
        settings.shadowBoost = Math.max(0, Math.min(1, settings.shadowBoost));
        settings.shadowThreshold = Math.max(0, Math.min(255, settings.shadowThreshold));
        settings.colorTemperature = Math.max(-100, Math.min(100, settings.colorTemperature));
        settings.sharpness = Math.max(0, Math.min(100, settings.sharpness));
        settings.antiNoise = Math.max(0, Math.min(1, settings.antiNoise));
        settings.smoothing = Math.max(0, Math.min(1, settings.smoothing));
        settings.vibrance = Math.max(0, Math.min(1, settings.vibrance));

        // Boolean validation
        settings.hdrEnabled = Boolean(settings.hdrEnabled);
        settings.processSVGs = Boolean(settings.processSVGs);
        settings.enableGUISettings = Boolean(settings.enableGUISettings);
        settings.lazyProcessing = Boolean(settings.lazyProcessing);
        settings.processOnlyVisible = Boolean(settings.processOnlyVisible);
        settings.autoDetectHDR = Boolean(settings.autoDetectHDR);
        settings.upscaleSmoothing = Boolean(settings.upscaleSmoothing);
        settings.videoEnhancement = Boolean(settings.videoEnhancement);

        // Array validation
        if (!Array.isArray(settings.excludedSites)) {
            settings.excludedSites = DEFAULT_SETTINGS.excludedSites;
        }

        // Presets validation
        if (!settings.presets || typeof settings.presets !== 'object') {
            settings.presets = DEFAULT_SETTINGS.presets;
        }
    }

    function saveSettings() {
        GM_setValue(SCRIPT_NAME, JSON.stringify(settings));
        window.dispatchEvent(new CustomEvent('autoHDRSettingsChanged'));
    }

    // --- Helper Functions (Ultra-Optimized) ---
    function isCrossOrigin(img) {
        const src = img.src;
        if (crossOriginCache.has(src)) {
            return crossOriginCache.get(src);
        }

        // Prevent memory leak by limiting cache size
        if (crossOriginCache.size > 1000) {
            const firstKey = crossOriginCache.keys().next().value;
            crossOriginCache.delete(firstKey);
        }

        try {
            if (src.startsWith('data:')) {
                crossOriginCache.set(src, false);
                return false;
            }
            const srcUrl = new URL(src, window.location.href);
            const isCrossOrigin = srcUrl.origin !== window.location.origin;
            crossOriginCache.set(src, isCrossOrigin);
            return isCrossOrigin;
        } catch (e) {
            crossOriginCache.set(src, true);
            return true;
        }
    }

    // Ultra-optimized constants for inline processing
    const SATURATION_WEIGHTS = [0.299, 0.587, 0.114];

    // Optimized visibility check with early return
    const isElementVisible = (element) => !settings.processOnlyVisible || (
        (rect => rect.top < window.innerHeight && rect.bottom > 0 && rect.left < window.innerWidth && rect.right > 0)
        (element.getBoundingClientRect())
    );

    // --- Advanced Image Processing Functions ---
    function applyNoiseReductionAndSmoothing(imageData, width, height, noiseStrength, smoothStrength) {
        const data = imageData.data;
        const tempData = new Uint8ClampedArray(data);

        // Noise reduction using bilateral filter approach
        if (noiseStrength > 0) {
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;

                    // Sample surrounding pixels for noise reduction
                    let rSum = 0, gSum = 0, bSum = 0, weightSum = 0;

                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const sampleIdx = ((y + dy) * width + (x + dx)) * 4;
                            const r = tempData[sampleIdx];
                            const g = tempData[sampleIdx + 1];
                            const b = tempData[sampleIdx + 2];

                            // Calculate spatial and intensity weights
                            const spatialWeight = 1.0 / (1.0 + dx * dx + dy * dy);
                            const intensityDiff = Math.abs(r - tempData[idx]) +
                                                Math.abs(g - tempData[idx + 1]) +
                                                Math.abs(b - tempData[idx + 2]);
                            const intensityWeight = Math.exp(-intensityDiff / (50 * noiseStrength));
                            const weight = spatialWeight * intensityWeight;

                            rSum += r * weight;
                            gSum += g * weight;
                            bSum += b * weight;
                            weightSum += weight;
                        }
                    }

                    if (weightSum > 0) {
                        const factor = noiseStrength;
                        data[idx] = tempData[idx] * (1 - factor) + (rSum / weightSum) * factor;
                        data[idx + 1] = tempData[idx + 1] * (1 - factor) + (gSum / weightSum) * factor;
                        data[idx + 2] = tempData[idx + 2] * (1 - factor) + (bSum / weightSum) * factor;
                    }
                }
            }
        }

        // Smoothing for pixelation reduction using Gaussian-like blur
        if (smoothStrength > 0) {
            const tempData2 = new Uint8ClampedArray(data);
            const kernel = [1, 2, 1, 2, 4, 2, 1, 2, 1]; // 3x3 Gaussian kernel
            const kernelSum = 16;

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;
                    let rSum = 0, gSum = 0, bSum = 0;
                    let kernelIdx = 0;

                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const sampleIdx = ((y + dy) * width + (x + dx)) * 4;
                            const weight = kernel[kernelIdx++];
                            rSum += tempData2[sampleIdx] * weight;
                            gSum += tempData2[sampleIdx + 1] * weight;
                            bSum += tempData2[sampleIdx + 2] * weight;
                        }
                    }

                    const factor = smoothStrength;
                    data[idx] = tempData2[idx] * (1 - factor) + (rSum / kernelSum) * factor;
                    data[idx + 1] = tempData2[idx + 1] * (1 - factor) + (gSum / kernelSum) * factor;
                    data[idx + 2] = tempData2[idx + 2] * (1 - factor) + (bSum / kernelSum) * factor;
                }
            }
        }
    }

    // --- Ultra-Optimized HDR Application ---
    function applyHDREffectToImage(img) {
        // Ultra-fast early exits
        if (img.dataset.hdrApplied || processedElements.has(img)) return;

        // Combined validation checks
        if (!img.complete || img.naturalWidth === 0 || img.naturalHeight === 0) {
            if (img.naturalWidth === 0 && img.complete) img.dataset.hdrApplied = 'invalid-dimensions';
            return;
        }

        // Auto-detect HDR content and skip if already HDR
        if (settings.autoDetectHDR && img.src.toLowerCase().includes('hdr')) {
            img.dataset.hdrApplied = 'skipped-hdr-detected';
            processedElements.add(img);
            return;
        }

        // Lazy processing check
        if (!isElementVisible(img)) return;

        const src = img.src;

        // SVG check with early return
        if ((src.includes('.svg') || src.startsWith('data:image/svg+xml')) && !settings.processSVGs) {
            img.dataset.hdrApplied = 'skipped-svg';
            processedElements.add(img);
            return;
        }

        // Size and cross-origin checks with CSS fallback
        if (img.naturalWidth > settings.maxCanvasDimension || img.naturalHeight > settings.maxCanvasDimension || isCrossOrigin(img)) {
            // Pre-compiled CSS filter for performance (Note: CSS doesn't support vibrance, only saturation)
            if (!cssFilterString) {
                // Combine saturation and vibrance for CSS fallback (approximate effect)
                const combinedSaturation = settings.saturation + (settings.vibrance * 0.5);
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${combinedSaturation})`;
            }
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = img.naturalWidth > settings.maxCanvasDimension ? 'css-only-large' : 'css-only-crossorigin';
            processedElements.add(img);
            return;
        }

        // Canvas processing for full HDR effect
        processImageWithCanvas(img);
    }

    // Ultra-optimized canvas processing
    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Pre-calculate all values for maximum performance
            const { brightness, contrast, saturation, vibrance, highlightReduction, highlightThreshold,
                    shadowBoost, shadowThreshold, colorTemperature, sharpness, antiNoise, smoothing } = settings;
            const needsSaturation = saturation !== 1.0;
            const needsVibrance = vibrance > 0;
            const needsHighlightReduction = highlightReduction > 0;
            const needsShadowBoost = shadowBoost > 0;
            const needsColorTemp = colorTemperature !== 0;
            const needsSharpness = sharpness > 0;
            const needsAntiNoise = antiNoise > 0;
            const needsSmoothing = smoothing > 0;
            const contrastOffset = 128;

            // Color temperature adjustment factors
            const tempFactor = colorTemperature / 100;
            const warmth = tempFactor > 0 ? tempFactor : 0;
            const coolness = tempFactor < 0 ? -tempFactor : 0;

            // Ultra-optimized pixel processing with enhanced features
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i], g = data[i + 1], b = data[i + 2];

                // Combined contrast and brightness in single operation
                r = ((r - contrastOffset) * contrast + contrastOffset) * brightness;
                g = ((g - contrastOffset) * contrast + contrastOffset) * brightness;
                b = ((b - contrastOffset) * contrast + contrastOffset) * brightness;

                // Shadow boost for better dynamic range
                if (needsShadowBoost) {
                    const luminance = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;
                    if (luminance < shadowThreshold) {
                        const shadowFactor = 1 + shadowBoost * (1 - luminance / shadowThreshold);
                        r *= shadowFactor;
                        g *= shadowFactor;
                        b *= shadowFactor;
                    }
                }

                // Color temperature adjustment
                if (needsColorTemp) {
                    if (warmth > 0) {
                        r += warmth * 20;
                        g += warmth * 10;
                        b -= warmth * 15;
                    } else if (coolness > 0) {
                        r -= coolness * 15;
                        g -= coolness * 5;
                        b += coolness * 20;
                    }
                }

                // Saturation enhancement
                if (needsSaturation) {
                    const gray = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;
                    r = gray + (r - gray) * saturation;
                    g = gray + (g - gray) * saturation;
                    b = gray + (b - gray) * saturation;
                }

                // Vibrance enhancement (smart saturation that protects skin tones and already saturated colors)
                if (needsVibrance) {
                    const gray = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;

                    // Calculate current saturation level
                    const maxChannel = Math.max(r, g, b);
                    const minChannel = Math.min(r, g, b);
                    const currentSaturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;

                    // Skin tone detection (simple heuristic: warm colors with specific ratios)
                    const isWarmTone = r > g && g > b && r > 1.2 * g && g > 0.8 * b;
                    const skinToneProtection = isWarmTone ? 0.3 : 1.0;

                    // Apply vibrance with protection for already saturated colors and skin tones
                    const vibranceStrength = vibrance * (1 - currentSaturation * 0.7) * skinToneProtection;

                    if (vibranceStrength > 0) {
                        r = gray + (r - gray) * (1 + vibranceStrength);
                        g = gray + (g - gray) * (1 + vibranceStrength);
                        b = gray + (b - gray) * (1 + vibranceStrength);
                    }
                }

                // Highlight reduction for natural look
                if (needsHighlightReduction) {
                    const invReduction = 1 - highlightReduction;
                    if (r > highlightThreshold) r = highlightThreshold + (r - highlightThreshold) * invReduction;
                    if (g > highlightThreshold) g = highlightThreshold + (g - highlightThreshold) * invReduction;
                    if (b > highlightThreshold) b = highlightThreshold + (b - highlightThreshold) * invReduction;
                }

                // Inline clamping for maximum performance
                data[i] = r < 0 ? 0 : r > 255 ? 255 : Math.round(r);
                data[i + 1] = g < 0 ? 0 : g > 255 ? 255 : Math.round(g);
                data[i + 2] = b < 0 ? 0 : b > 255 ? 255 : Math.round(b);
            }

            // Apply noise reduction and smoothing if needed
            if (needsAntiNoise || needsSmoothing) {
                applyNoiseReductionAndSmoothing(imageData, canvas.width, canvas.height, antiNoise, smoothing);
            }

            ctx.putImageData(imageData, 0, 0);

            // Apply sharpness filter if needed
            if (needsSharpness) {
                const sharpnessFilter = `contrast(${1 + sharpness / 200}) brightness(${1 + sharpness / 400})`;
                canvas.style.filter = sharpnessFilter;
            }

            // Efficient src handling
            if (!img.dataset.originalSrc && !img.src.startsWith('data:')) img.dataset.originalSrc = img.src;
            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas-processed';
            processedElements.add(img);

        } catch (e) {
            // Fallback with pre-compiled filter (combine saturation and vibrance for CSS)
            if (!cssFilterString) {
                const combinedSaturation = settings.saturation + (settings.vibrance * 0.5);
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${combinedSaturation})`;
            }
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'error-canvas css-fallback';
            processedElements.add(img);
        }
    }

    // Enhanced video processing with anti-noise and smoothing
    function applyHDRToVideos() {
        const videos = document.querySelectorAll('video:not([data-hdrApplied="video-processed"])');
        if (videos.length === 0) return;

        videos.forEach(video => {
            if (isElementVisible(video)) {
                // Build enhanced filter string for videos (combine saturation and vibrance for CSS)
                const combinedSaturation = settings.saturation + (settings.vibrance * 0.5);
                let filterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${combinedSaturation})`;

                // Add video enhancement filters if enabled
                if (settings.videoEnhancement) {
                    // Anti-aliasing and smoothing for pixelated videos
                    if (settings.upscaleSmoothing) {
                        video.classList.add('hdr-enhanced-video');
                    }

                    // Add noise reduction and smoothing via CSS filters
                    if (settings.antiNoise > 0) {
                        const blurAmount = settings.antiNoise * 0.5; // Convert to blur radius
                        filterString += ` blur(${blurAmount}px)`;
                    }

                    if (settings.smoothing > 0) {
                        // Use CSS filters to simulate smoothing
                        const smoothAmount = 1 + settings.smoothing * 0.1;
                        filterString += ` contrast(${smoothAmount})`;
                    }

                    // Enhanced sharpening for videos
                    if (settings.sharpness > 0) {
                        const sharpAmount = 1 + settings.sharpness / 100;
                        filterString += ` contrast(${sharpAmount}) brightness(${1 + settings.sharpness / 400})`;
                    }
                }

                video.style.filter = filterString;
                video.dataset.hdrApplied = 'video-processed';
                processedElements.add(video);

                // Apply additional video-specific enhancements
                if (settings.videoEnhancement) {
                    enhanceVideoPlayback(video);
                }
            }
        });
    }

    // Enhanced video playback optimization
    function enhanceVideoPlayback(video) {
        // Optimize video rendering for better quality
        if (settings.upscaleSmoothing) {
            // Set optimal image rendering for video upscaling
            video.style.imageRendering = 'auto';
            // Add CSS class for additional styling if needed
            video.classList.add('hdr-enhanced-video');
        }

        // Add event listener for dynamic quality adjustment
        if (!video.dataset.qualityEnhanced) {
            video.addEventListener('loadedmetadata', () => {
                // Detect low resolution videos and apply additional smoothing
                if (video.videoWidth < 720 && settings.upscaleSmoothing) {
                    video.style.imageRendering = 'auto';
                    video.style.transform = 'scale(1.001)'; // Slight scale to trigger better interpolation
                }
            });

            video.addEventListener('play', () => {
                // Optimize during playback
                if (settings.videoEnhancement) {
                    video.style.willChange = 'filter, transform';
                }
            });

            video.addEventListener('pause', () => {
                // Clean up optimization
                video.style.willChange = 'auto';
            });

            video.dataset.qualityEnhanced = 'true';
        }
    }

    // Ultra-optimized intersection observer
    function setupIntersectionObserver() {
        if (!settings.lazyProcessing || intersectionObserver) return;

        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const tagName = element.tagName;

                    if (tagName === 'IMG' && !element.dataset.hdrApplied) {
                        applyHDREffectToImage(element);
                    } else if (tagName === 'VIDEO' && !element.dataset.hdrApplied) {
                        // Build enhanced filter string for videos
                        let filterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;

                        // Add video enhancement filters if enabled
                        if (settings.videoEnhancement) {
                            if (settings.antiNoise > 0) {
                                const blurAmount = settings.antiNoise * 0.5;
                                filterString += ` blur(${blurAmount}px)`;
                            }
                            if (settings.smoothing > 0) {
                                const smoothAmount = 1 + settings.smoothing * 0.1;
                                filterString += ` contrast(${smoothAmount})`;
                            }
                            if (settings.sharpness > 0) {
                                const sharpAmount = 1 + settings.sharpness / 100;
                                filterString += ` contrast(${sharpAmount}) brightness(${1 + settings.sharpness / 400})`;
                            }
                        }

                        element.style.filter = filterString;
                        element.dataset.hdrApplied = 'video-processed';
                        processedElements.add(element);

                        // Apply video enhancement if enabled
                        if (settings.videoEnhancement) {
                            enhanceVideoPlayback(element);
                        }
                    }
                    intersectionObserver.unobserve(element);
                }
            });
        }, { rootMargin: '50px', threshold: 0.1 });
    }

    const cleanupIntersectionObserver = () => {
        if (intersectionObserver) {
            intersectionObserver.disconnect();
            intersectionObserver = null;
        }
    };

    // Ultra-optimized revert function
    const revertElement = (el) => {
        if (!el.dataset.hdrApplied) return;

        const wasCanvasProcessed = el.dataset.hdrApplied.includes('canvas-processed');
        const originalSrc = el.dataset.originalSrc;

        el.style.filter = '';

        // Efficient src restoration
        if (wasCanvasProcessed && originalSrc && el.src?.startsWith('data:image') && el.src !== originalSrc) {
            el.src = originalSrc;
        }

        // Clean up video enhancements
        if (el.tagName === 'VIDEO') {
            el.classList.remove('hdr-enhanced-video');
            el.style.imageRendering = '';
            el.style.transform = '';
            el.style.willChange = '';
        }

        // Batch attribute removal
        ['data-hdrApplied', 'data-originalSrc', 'data-hdrListener', 'data-qualityEnhanced'].forEach(attr => {
            if (el.hasAttribute(attr)) el.removeAttribute(attr);
        });

        processedElements.delete(el);
    };


    // Ultra-optimized media processing
    function processAllMedia() {
        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        // Lazy processing setup
        if (settings.lazyProcessing) setupIntersectionObserver();

        // Efficient image processing
        const images = document.querySelectorAll('img:not([data-hdrApplied])');
        images.forEach(img => {
            if (settings.lazyProcessing && intersectionObserver) {
                intersectionObserver.observe(img);
            } else if (img.complete) {
                applyHDREffectToImage(img);
            } else if (!img.dataset.hdrListener) {
                // Inline load listener for efficiency
                const onLoad = () => {
                    applyHDREffectToImage(img);
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                const onError = () => {
                    img.dataset.hdrApplied = 'error-load';
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
                img.dataset.hdrListener = 'true';
            }
        });

        applyHDRToVideos();
    }

    // --- Site Exclusion & Debounce (Ultra-Optimized) ---
    function isSiteExcluded() {
        const currentHref = window.location.href;

        // Use cache if URL hasn't changed
        if (currentHref === lastHref && siteExclusionCache !== null) {
            return siteExclusionCache;
        }

        lastHref = currentHref;

        if (!Array.isArray(settings.excludedSites) || settings.excludedSites.length === 0) {
            siteExclusionCache = false;
            return false;
        }

        siteExclusionCache = settings.excludedSites.some(site =>
            site && typeof site === 'string' && site.trim() !== '' && currentHref.includes(site.trim())
        );

        return siteExclusionCache;
    }

    // Ultra-optimized debounce
    const debounce = (fn, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    };

    // Optimized for responsiveness
    const debouncedProcessMedia = debounce(processAllMedia, 100);

    // Ultra-optimized mutation observer
    function startObserver() {
        if (mutationObserver) mutationObserver.disconnect();

        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        mutationObserver = new MutationObserver((mutationsList) => {
            let needsProcessing = false;

            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Ultra-efficient media element detection
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === 1) { // ELEMENT_NODE
                            const tagName = node.tagName;
                            if (tagName === 'IMG' || tagName === 'VIDEO' ||
                                (node.querySelector && node.querySelector('img, video'))) {
                                needsProcessing = true;
                                break;
                            }
                        }
                    }
                } else if (mutation.type === 'attributes') {
                    const target = mutation.target;
                    const tagName = target.tagName;

                    if ((tagName === 'IMG' || tagName === 'VIDEO') && target.dataset.hdrApplied) {
                        if ((mutation.attributeName === 'src' && !target.src.startsWith('data:image')) ||
                            (mutation.attributeName === 'style' && !target.style.filter?.includes('brightness'))) {
                            revertElement(target);
                            needsProcessing = true;
                        }
                    }
                }

                if (needsProcessing) break;
            }

            if (needsProcessing) debouncedProcessMedia();
        });

        const target = document.body || document.documentElement;
        if (target) {
            mutationObserver.observe(target, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['src', 'style']
            });
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                mutationObserver.observe(document.body || document.documentElement, {
                    childList: true, subtree: true, attributes: true, attributeFilter: ['src', 'style']
                });
            }, { once: true });
        }

        debouncedProcessMedia();
    }


    function init() {
        loadSettings();
        // console.log("AutoHDR Initialized. Settings:", JSON.parse(JSON.stringify(settings))); // Deep copy for logging

        if (settings.enableGUISettings) {
            // Wait for body to ensure GUI can be appended
            if (document.body) {
                createSettingsGUI();
            } else {
                document.addEventListener('DOMContentLoaded', createSettingsGUI, { once: true });
            }
        }

        startObserver();

        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                // console.log("AutoHDR: Settings changed via localStorage.");
                const oldEnabled = settings.hdrEnabled;
                const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                loadSettings();

                const newExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                // If enabled status changed, or excluded sites changed, or if it's still enabled (other settings might have changed)
                if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                    // console.log("AutoHDR: Re-evaluating observer and media due to settings change from storage event.");
                    startObserver(); // This will disconnect, revert if needed, and then restart/reprocess.
                }
            }
        });

        window.addEventListener('autoHDRSettingsChanged', () => {
            // console.log("AutoHDR: Settings changed via custom event (save from GUI).");
            const oldEnabled = settings.hdrEnabled;
            const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);
            // loadSettings(); // Settings are already updated by GUI before saveSettings() -> event.
                              // But if saveSettings() was called externally without updating 'settings' var first, then load would be needed.
                              // For now, GUI ensures 'settings' is fresh.

            const newExcludedSitesJSON = JSON.stringify(settings.excludedSites); // settings should be fresh from GUI update.

            if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                // console.log("AutoHDR: Re-evaluating observer and media due to settings change from custom event.");
                startObserver();
            }
        });

        if (document.readyState === 'complete') {
            debouncedProcessMedia();
        } else {
            window.addEventListener('load', debouncedProcessMedia, { once: true });
        }
    }

// --- Settings GUI (Sederhana) ---
    function createSettingsGUI() {
        if (document.getElementById('autohdr-settings-button') || !document.body) return;

        GM_addStyle(`
            /* Minimalist HDR Settings UI */
            #autohdr-settings-button {
                position: fixed;
                top: 50%;
                left: -20px;
                z-index: 2147483646;
                background: rgba(0, 0, 0, 0.8);
                color: #fff;
                border: none;
                padding: 10px 8px;
                border-radius: 0 6px 6px 0;
                cursor: pointer;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 14px;
                font-weight: 400;
                opacity: 0.6;
                transition: all 0.2s ease;
                user-select: none;
                backdrop-filter: blur(8px);
                transform: translateY(-50%);
                width: 28px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 1;
            }
            #autohdr-settings-button:hover {
                left: 0px;
                opacity: 1;
                background: rgba(0, 0, 0, 0.9);
                transform: translateY(-50%) scale(1.05);
            }
            #autohdr-settings-panel {
                position: fixed;
                top: 50%;
                left: -320px;
                z-index: 2147483645;
                background: rgba(15, 15, 15, 0.98);
                color: #e8e8e8;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 0 12px 12px 0;
                padding: 0;
                display: none;
                flex-direction: column;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 12px;
                width: 300px;
                max-height: 85vh;
                overflow: hidden;
                box-shadow: 0 12px 48px rgba(0,0,0,0.6);
                backdrop-filter: blur(16px);
                transform: translateY(-50%);
                transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
            }
            #autohdr-settings-panel.show {
                left: 0px;
                opacity: 1;
            }

            /* Panel Header */
            .autohdr-header {
                padding: 16px 20px;
                background: rgba(255, 255, 255, 0.05);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                position: sticky;
                top: 0;
                z-index: 10;
            }

            /* Panel Content */
            .autohdr-content {
                padding: 16px 20px;
                overflow-y: auto;
                flex: 1;
                gap: 14px;
                display: flex;
                flex-direction: column;
            }

            /* Custom Scrollbar */
            .autohdr-content::-webkit-scrollbar {
                width: 6px;
            }
            .autohdr-content::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.05);
            }
            .autohdr-content::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;
            }
            .autohdr-content::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            @keyframes slideInFromLeft {
                from {
                    opacity: 0;
                    left: -320px;
                    transform: translateY(-50%) translateX(-20px);
                }
                to {
                    opacity: 1;
                    left: 0px;
                    transform: translateY(-50%) translateX(0);
                }
            }
            /* Control Groups */
            .autohdr-control-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-bottom: 6px;
            }

            #autohdr-settings-panel label {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0;
                font-weight: 400;
                font-size: 11px;
                color: #d0d0d0;
                padding: 6px 0;
            }

            /* Input Styling */
            #autohdr-settings-panel input[type="number"],
            #autohdr-settings-panel input[type="text"],
            #autohdr-settings-panel textarea {
                width: 65px;
                background: rgba(255, 255, 255, 0.06);
                color: #e8e8e8;
                border: 1px solid rgba(255, 255, 255, 0.12);
                padding: 6px 8px;
                border-radius: 6px;
                transition: all 0.2s ease;
                font-size: 11px;
                font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            }

            #autohdr-settings-panel input[type="number"]:focus,
            #autohdr-settings-panel input[type="text"]:focus,
            #autohdr-settings-panel textarea:focus {
                outline: none;
                border-color: rgba(96, 165, 250, 0.5);
                background: rgba(255, 255, 255, 0.1);
                box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.1);
            }

            #autohdr-settings-panel input[type="checkbox"] {
                margin-right: 8px;
                transform: scale(1.15);
                accent-color: #60a5fa;
                cursor: pointer;
            }

            #autohdr-settings-panel textarea {
                width: 100%;
                min-height: 45px;
                margin-top: 6px;
                resize: vertical;
                font-family: inherit;
                line-height: 1.4;
            }
            /* Save Button */
            #autohdr-settings-panel button#autohdr-save-settings {
                padding: 12px 20px;
                background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
                color: #ffffff;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                font-size: 12px;
                transition: all 0.2s ease;
                margin-top: 16px;
                width: 100%;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            #autohdr-settings-panel button#autohdr-save-settings:hover {
                background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.4);
            }
            #autohdr-settings-panel button#autohdr-save-settings:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
            }

            /* Title */
            #autohdr-settings-panel .autohdr-title {
                font-weight: 700;
                font-size: 16px;
                margin: 0;
                text-align: center;
                color: #ffffff;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            /* Section Titles */
            #autohdr-settings-panel .autohdr-section-title {
                font-weight: 600;
                margin: 16px 0 8px 0;
                color: #60a5fa;
                font-size: 12px;
                text-transform: uppercase;
                letter-spacing: 1px;
                position: relative;
                padding-left: 12px;
            }
            #autohdr-settings-panel .autohdr-section-title:before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 12px;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                border-radius: 2px;
            }

            /* Special styling for Image Enhancement section - using attribute selector instead */
            #autohdr-settings-panel .autohdr-section-title[data-section="enhancement"]:before {
                background: linear-gradient(135deg, #10b981, #059669);
            }

            /* Enhanced input styling for new features */
            #autohdr-settings-panel input[id="antiNoise"],
            #autohdr-settings-panel input[id="smoothing"] {
                background: rgba(16, 185, 129, 0.06);
                border-color: rgba(16, 185, 129, 0.2);
            }

            #autohdr-settings-panel input[id="antiNoise"]:focus,
            #autohdr-settings-panel input[id="smoothing"]:focus {
                border-color: rgba(16, 185, 129, 0.5);
                box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
            }

            /* Vibrance input styling */
            #autohdr-settings-panel input[id="vibrance"] {
                background: rgba(168, 85, 247, 0.06);
                border-color: rgba(168, 85, 247, 0.2);
            }

            #autohdr-settings-panel input[id="vibrance"]:focus {
                border-color: rgba(168, 85, 247, 0.5);
                box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.1);
            }
            /* Preset Buttons Container */
            .autohdr-presets-container {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin: 12px 0;
            }

            /* Enhanced Preset Buttons */
            .autohdr-preset-btn {
                background: linear-gradient(135deg, rgba(96, 165, 250, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
                border: 1px solid rgba(96, 165, 250, 0.3);
                border-radius: 8px;
                color: #e8e8e8;
                cursor: pointer;
                padding: 12px 8px;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 11px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 4px;
                min-height: 70px;
                backdrop-filter: blur(4px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                opacity: 0;
                transform: translateY(20px);
                animation: slideInUp 0.6s ease forwards;
            }

            .autohdr-preset-btn:before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
                transition: left 0.5s ease;
            }

            .autohdr-preset-btn:hover {
                background: linear-gradient(135deg, rgba(96, 165, 250, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
                border-color: rgba(96, 165, 250, 0.6);
                transform: translateY(-2px) scale(1.02);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2), 0 0 20px rgba(96, 165, 250, 0.1);
            }

            .autohdr-preset-btn:hover .preset-icon {
                animation: float 1.5s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
            }

            @keyframes glow {
                from { filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)); }
                to { filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) drop-shadow(0 0 8px rgba(96, 165, 250, 0.4)); }
            }

            .autohdr-preset-btn:hover:before {
                left: 100%;
            }

            .autohdr-preset-btn:active {
                transform: translateY(0) scale(0.98);
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
            }

            .autohdr-preset-btn .preset-icon {
                font-size: 18px;
                margin-bottom: 2px;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
                animation: float 3s ease-in-out infinite;
            }

            .autohdr-preset-btn .preset-name {
                font-weight: 600;
                font-size: 11px;
                color: #ffffff;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
                letter-spacing: 0.5px;
            }

            .autohdr-preset-btn .preset-desc {
                font-size: 9px;
                color: rgba(255, 255, 255, 0.7);
                font-weight: 400;
                text-align: center;
                line-height: 1.2;
            }

            /* Ripple Effect */
            .ripple-effect {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }

            /* Entrance Animation */
            @keyframes slideInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Floating animation for icons */
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-3px); }
            }

            /* Preset button click effect */
            .autohdr-preset-btn.clicked {
                background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(22, 163, 74, 0.2) 100%);
                border-color: rgba(34, 197, 94, 0.6);
                animation: clickPulse 0.6s ease;
            }

            @keyframes clickPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            /* Notification Styles */
            .autohdr-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 2147483647;
                background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(22, 163, 74, 0.95) 100%);
                color: #ffffff;
                padding: 12px 20px;
                border-radius: 8px;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 12px;
                font-weight: 500;
                box-shadow: 0 8px 32px rgba(34, 197, 94, 0.3);
                backdrop-filter: blur(16px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transform: translateX(400px);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                align-items: center;
                gap: 8px;
                max-width: 300px;
            }

            .autohdr-notification.show {
                transform: translateX(0);
            }

            .autohdr-notification.error {
                background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%);
                box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
            }

            .autohdr-notification::before {
                content: '✓';
                font-size: 14px;
                font-weight: bold;
            }

            .autohdr-notification.error::before {
                content: '✗';
            }

            /* Video Enhancement Styles */
            .hdr-enhanced-video {
                image-rendering: auto !important;
                image-rendering: -webkit-optimize-contrast !important;
            }

            /* Performance optimization */
            #autohdr-settings-button, #autohdr-settings-panel {
                will-change: transform, opacity;
            }
        `);

        const panel = document.createElement('div');
        panel.id = 'autohdr-settings-panel';

        // Create header
        const header = document.createElement('div');
        header.className = 'autohdr-header';
        header.appendChild(createElementWithProps('div', { className: 'autohdr-title', textContent: 'Auto HDR Settings' }));
        panel.appendChild(header);

        // Create content area
        const content = document.createElement('div');
        content.className = 'autohdr-content';
        panel.appendChild(content);

        // Helper function to create elements
        function createElementWithProps(tag, props, children = []) {
            const el = document.createElement(tag);
            for (const key in props) {
                if (key === 'textContent') el.textContent = props[key];
                else if (key === 'className') el.className = props[key];
                else el.setAttribute(key, props[key]);
            }
            children.forEach(child => el.appendChild(child));
            return el;
        }

        // Helper function to create a label with an input
        function createLabelWithInput(labelText, inputProps, isCheckbox = false, inputTag = 'input') {
            const input = createElementWithProps(inputTag, inputProps);
            const label = createElementWithProps('label', { textContent: labelText + (isCheckbox ? '' : ': ') });
            if (isCheckbox) {
                label.insertBefore(input, label.firstChild);
                label.appendChild(document.createTextNode(labelText));
                input.style.marginRight = '5px';
                label.firstChild.nextSibling.remove();
            } else {
                label.appendChild(input);
            }
            return label;
        }

        // Helper function to create a label with an input (checkbox style: input first)
        function createCheckboxLabel(labelText, inputProps) {
            const input = createElementWithProps('input', { type: 'checkbox', ...inputProps });
            const label = document.createElement('label');
            label.appendChild(input);
            label.appendChild(document.createTextNode(' ' + labelText));
            return label;
        }

        // Helper function to create control group
        function createControlGroup(children) {
            const group = document.createElement('div');
            group.className = 'autohdr-control-group';
            children.forEach(child => group.appendChild(child));
            return group;
        }

        // HDR Enabled
        content.appendChild(createCheckboxLabel('Enable HDR', { id: 'hdrEnabled' }));

        // Adjustments Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Basic Adjustments' }));
        content.appendChild(createControlGroup([
            createLabelWithInput('Brightness', { id: 'brightness', type: 'number', step: '0.01', min: '0' }),
            createLabelWithInput('Contrast', { id: 'contrast', type: 'number', step: '0.01', min: '0' }),
            createLabelWithInput('Saturation', { id: 'saturation', type: 'number', step: '0.01', min: '0' }),
            createLabelWithInput('Vibrance', { id: 'vibrance', type: 'number', step: '0.01', min: '0', max: '1' })
        ]));

        // Highlights & Shadows Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Highlights & Shadows' }));
        content.appendChild(createControlGroup([
            createLabelWithInput('Highlight Reduction', { id: 'highlightReduction', type: 'number', step: '0.01', min: '0', max: '1' }),
            createLabelWithInput('Highlight Threshold', { id: 'highlightThreshold', type: 'number', step: '1', min: '0', max: '255' }),
            createLabelWithInput('Shadow Boost', { id: 'shadowBoost', type: 'number', step: '0.01', min: '0', max: '1' }),
            createLabelWithInput('Shadow Threshold', { id: 'shadowThreshold', type: 'number', step: '1', min: '0', max: '255' })
        ]));

        // Color & Temperature Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Color & Temperature' }));
        content.appendChild(createControlGroup([
            createLabelWithInput('Color Temperature', { id: 'colorTemperature', type: 'number', step: '1', min: '-100', max: '100' }),
            createLabelWithInput('Sharpness', { id: 'sharpness', type: 'number', step: '1', min: '0', max: '100' })
        ]));

        // Image Enhancement Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', 'data-section': 'enhancement', textContent: 'Image Enhancement' }));
        content.appendChild(createControlGroup([
            createLabelWithInput('Anti-Noise', { id: 'antiNoise', type: 'number', step: '0.01', min: '0', max: '1' }),
            createLabelWithInput('Smoothing', { id: 'smoothing', type: 'number', step: '0.01', min: '0', max: '1' }),
            createCheckboxLabel('Upscale Smoothing', { id: 'upscaleSmoothing' }),
            createCheckboxLabel('Video Enhancement', { id: 'videoEnhancement' })
        ]));

        // Performance & Misc Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Performance & Options' }));
        content.appendChild(createControlGroup([
            createLabelWithInput('Max Canvas Dim (px)', { id: 'maxCanvasDimension', type: 'number', step: '100', min: '200' }),
            createCheckboxLabel('Process SVGs (Canvas)', { id: 'processSVGs' }),
            createCheckboxLabel('Lazy Processing (Viewport)', { id: 'lazyProcessing' }),
            createCheckboxLabel('Process Only Visible', { id: 'processOnlyVisible' }),
            createCheckboxLabel('Auto-detect HDR Content', { id: 'autoDetectHDR' })
        ]));

        // Quick Presets Section
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Quick Presets' }));
        const presetsContainer = document.createElement('div');
        presetsContainer.className = 'autohdr-presets-container';

        // Preset icons and descriptions
        const presetConfig = {
            natural: { icon: '🌿', desc: 'Natural look' },
            vivid: { icon: '🌈', desc: 'Vibrant colors' },
            cinema: { icon: '🎬', desc: 'Movie-like' },
            gaming: { icon: '🎮', desc: 'Gaming optimized' }
        };

        Object.keys(settings.presets).forEach((presetName, index) => {
            const config = presetConfig[presetName] || { icon: '⚙️', desc: 'Custom preset' };
            const presetBtn = document.createElement('button');
            presetBtn.className = 'autohdr-preset-btn';
            presetBtn.style.animationDelay = `${index * 0.1}s`;
            presetBtn.innerHTML = `
                <span class="preset-icon">${config.icon}</span>
                <span class="preset-name">${presetName.charAt(0).toUpperCase() + presetName.slice(1)}</span>
                <span class="preset-desc">${config.desc}</span>
            `;

            // Add enhanced click handler with haptic feedback simulation
            presetBtn.addEventListener('click', (e) => {
                e.preventDefault();
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.className = 'ripple-effect';
                const rect = presetBtn.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                presetBtn.appendChild(ripple);

                setTimeout(() => ripple.remove(), 600);
                applyPreset(presetName, e);
            });

            presetsContainer.appendChild(presetBtn);
        });
        content.appendChild(presetsContainer);

        // Excluded Sites
        content.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Excluded Sites' }));
        const excludedSitesLabel = createLabelWithInput('Sites to exclude (comma-separated)',
            { id: 'excludedSites', title: "Comma-separated list of partial URLs to exclude. E.g., 'google.com, anothersite.net/path'" },
            false,
            'textarea'
        );
        content.appendChild(excludedSitesLabel);

        // Save Button
        content.appendChild(createElementWithProps('button', { id: 'autohdr-save-settings', textContent: 'Save & Apply' }));

        document.body.appendChild(panel);

        // Populate GUI with current settings (optimized)
        function populateGUISettings() {
            const elements = {
                hdrEnabled: settings.hdrEnabled,
                brightness: settings.brightness.toFixed(2),
                contrast: settings.contrast.toFixed(2),
                saturation: settings.saturation.toFixed(2),
                vibrance: settings.vibrance.toFixed(2),
                highlightReduction: settings.highlightReduction.toFixed(2),
                highlightThreshold: settings.highlightThreshold,
                shadowBoost: settings.shadowBoost.toFixed(2),
                shadowThreshold: settings.shadowThreshold,
                colorTemperature: settings.colorTemperature,
                sharpness: settings.sharpness,
                antiNoise: settings.antiNoise.toFixed(2),
                smoothing: settings.smoothing.toFixed(2),
                maxCanvasDimension: settings.maxCanvasDimension,
                processSVGs: settings.processSVGs,
                lazyProcessing: settings.lazyProcessing,
                processOnlyVisible: settings.processOnlyVisible,
                autoDetectHDR: settings.autoDetectHDR,
                upscaleSmoothing: settings.upscaleSmoothing,
                videoEnhancement: settings.videoEnhancement,
                excludedSites: settings.excludedSites.join(', ')
            };

            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = value;
                    } else {
                        element.value = value;
                    }
                }
            }
        }

        populateGUISettings();

        // Notification function
        function showNotification(message, type = 'success') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.autohdr-notification');
            existingNotifications.forEach(notif => notif.remove());

            const notification = document.createElement('div');
            notification.className = `autohdr-notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Trigger animation
            setTimeout(() => notification.classList.add('show'), 10);

            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 400);
            }, 3000);
        }

        // Enhanced apply preset function with visual feedback
        function applyPreset(presetName, clickEvent) {
            const preset = settings.presets[presetName];
            if (!preset) return;

            // Find the clicked button for visual feedback
            const clickedBtn = clickEvent ? clickEvent.target.closest('.autohdr-preset-btn') : null;
            if (clickedBtn) {
                // Add click effect
                clickedBtn.classList.add('clicked');
                setTimeout(() => clickedBtn.classList.remove('clicked'), 600);

                // Show loading state
                const originalHTML = clickedBtn.innerHTML;
                clickedBtn.innerHTML = `
                    <span class="preset-icon">⚡</span>
                    <span class="preset-name">Applying...</span>
                    <span class="preset-desc">Please wait</span>
                `;
                clickedBtn.style.pointerEvents = 'none';

                // Apply preset after short delay for visual feedback
                setTimeout(() => {
                    Object.assign(settings, preset);

                    // Animate input changes
                    const inputs = ['brightness', 'contrast', 'saturation', 'vibrance'];
                    inputs.forEach(inputId => {
                        const input = document.getElementById(inputId);
                        if (input && settings[inputId] !== undefined) {
                            input.style.transition = 'all 0.3s ease';
                            input.style.background = 'rgba(34, 197, 94, 0.2)';
                            input.value = settings[inputId].toFixed(2);
                            setTimeout(() => {
                                input.style.background = '';
                            }, 500);
                        }
                    });

                    populateGUISettings();

                    // Clear caches when preset is applied
                    crossOriginCache.clear();
                    siteExclusionCache = null;
                    cssFilterString = '';

                    saveSettings();

                    // Restore button
                    setTimeout(() => {
                        clickedBtn.innerHTML = originalHTML;
                        clickedBtn.style.pointerEvents = '';
                    }, 300);

                    // Show success notification
                    showNotification(`${presetName.charAt(0).toUpperCase() + presetName.slice(1)} preset applied!`, 'success');
                }, 200);
            }
        }

        const button = document.createElement('button');
        button.id = 'autohdr-settings-button';
        button.textContent = '▶'; // Simbol panah kanan
        button.title = 'HDR Settings'; // Tooltip

        // Hover functionality for floating button
        let hoverTimeout;

        button.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            const panelElement = document.getElementById('autohdr-settings-panel');
            panelElement.style.display = 'flex';
            setTimeout(() => panelElement.classList.add('show'), 10);
            populateGUISettings();
        });

        button.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                const panelElement = document.getElementById('autohdr-settings-panel');
                if (!panelElement.matches(':hover')) {
                    panelElement.classList.remove('show');
                    setTimeout(() => panelElement.style.display = 'none', 300);
                }
            }, 300);
        });

        // Click functionality as backup
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const panelElement = document.getElementById('autohdr-settings-panel');
            const isVisible = panelElement.classList.contains('show');

            if (isVisible) {
                panelElement.classList.remove('show');
                setTimeout(() => panelElement.style.display = 'none', 300);
            } else {
                panelElement.style.display = 'flex';
                setTimeout(() => panelElement.classList.add('show'), 10);
                populateGUISettings();
            }
        });

        document.body.appendChild(button);

        // Add hover functionality to panel
        panel.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
        });

        panel.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                panel.classList.remove('show');
                setTimeout(() => panel.style.display = 'none', 300);
            }, 300);
        });

        document.getElementById('autohdr-save-settings').addEventListener('click', () => {
            // Optimized settings update with validation
            const updates = {
                hdrEnabled: document.getElementById('hdrEnabled').checked,
                brightness: Math.max(0, parseFloat(document.getElementById('brightness').value) || DEFAULT_SETTINGS.brightness),
                contrast: Math.max(0, parseFloat(document.getElementById('contrast').value) || DEFAULT_SETTINGS.contrast),
                saturation: Math.max(0, parseFloat(document.getElementById('saturation').value) || DEFAULT_SETTINGS.saturation),
                vibrance: Math.max(0, Math.min(1, parseFloat(document.getElementById('vibrance').value) || DEFAULT_SETTINGS.vibrance)),
                highlightReduction: Math.max(0, Math.min(1, parseFloat(document.getElementById('highlightReduction').value) || DEFAULT_SETTINGS.highlightReduction)),
                highlightThreshold: Math.max(0, Math.min(255, parseInt(document.getElementById('highlightThreshold').value, 10) || DEFAULT_SETTINGS.highlightThreshold)),
                shadowBoost: Math.max(0, Math.min(1, parseFloat(document.getElementById('shadowBoost').value) || DEFAULT_SETTINGS.shadowBoost)),
                shadowThreshold: Math.max(0, Math.min(255, parseInt(document.getElementById('shadowThreshold').value, 10) || DEFAULT_SETTINGS.shadowThreshold)),
                colorTemperature: Math.max(-100, Math.min(100, parseInt(document.getElementById('colorTemperature').value, 10) || DEFAULT_SETTINGS.colorTemperature)),
                sharpness: Math.max(0, Math.min(100, parseInt(document.getElementById('sharpness').value, 10) || DEFAULT_SETTINGS.sharpness)),
                antiNoise: Math.max(0, Math.min(1, parseFloat(document.getElementById('antiNoise').value) || DEFAULT_SETTINGS.antiNoise)),
                smoothing: Math.max(0, Math.min(1, parseFloat(document.getElementById('smoothing').value) || DEFAULT_SETTINGS.smoothing)),
                maxCanvasDimension: Math.max(200, parseInt(document.getElementById('maxCanvasDimension').value, 10) || DEFAULT_SETTINGS.maxCanvasDimension),
                processSVGs: document.getElementById('processSVGs').checked,
                lazyProcessing: document.getElementById('lazyProcessing').checked,
                processOnlyVisible: document.getElementById('processOnlyVisible').checked,
                autoDetectHDR: document.getElementById('autoDetectHDR').checked,
                upscaleSmoothing: document.getElementById('upscaleSmoothing').checked,
                videoEnhancement: document.getElementById('videoEnhancement').checked
            };

            // Update excluded sites
            const excludedText = document.getElementById('excludedSites').value;
            updates.excludedSites = excludedText.split(',').map(s => s.trim()).filter(s => s !== '');

            // Apply all updates
            Object.assign(settings, updates);

            // Clear caches and reset filter string when settings change
            crossOriginCache.clear();
            siteExclusionCache = null;
            cssFilterString = '';

            saveSettings();
            const panelElement = document.getElementById('autohdr-settings-panel');
            panelElement.classList.remove('show');
            setTimeout(() => panelElement.style.display = 'none', 300);
        });

        document.addEventListener('click', (event) => {
            const panelElement = document.getElementById('autohdr-settings-panel');
            const buttonElement = document.getElementById('autohdr-settings-button');
            if (panelElement && buttonElement && panelElement.classList.contains('show') &&
                !panelElement.contains(event.target) && !buttonElement.contains(event.target)) {
                clearTimeout(hoverTimeout);
                panelElement.classList.remove('show');
                setTimeout(() => panelElement.style.display = 'none', 300);
            }
        });
    }

    // --- Jalankan Inisialisasi ---
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();
